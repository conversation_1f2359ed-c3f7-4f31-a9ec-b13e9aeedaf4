pipeline {
  agent any

  environment {
    REPO_URL = 'https://github.com/64SquaresApexLLP/AI-Studio-V2.git'
    FRONTEND_DIR = 'AI-Studio-V2/Frontend'
    BACKEND_DIR = 'AI-Studio-V2/Backend'
    BRANCH = 'main'
    BUILD_STATUS = 'SUCCESS'
    TEAM_EMAILS = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
  }

  triggers {
    githubPush()
  }

  stages {
    stage('Clone or Pull Repository') {
      steps {
        script {
          if (fileExists("${FRONTEND_DIR}/package.json")) {
            echo "Repo already exists, pulling latest changes..."
            dir('AI-Studio-V2') {
              sh "git reset --hard"
              sh "git pull origin ${BRANCH}"
            }
          } else {
            echo "Cloning repository..."
            withCredentials([usernamePassword(credentialsId: 'd71e0b5c-76a5-4c29-a92a-86ca39222877', usernameVariable: 'GIT_USER', passwordVariable: 'GIT_TOKEN')]) {
              sh "git clone https://${GIT_USER}:${GIT_TOKEN}@github.com/64SquaresApexLLP/AI-Studio-V2.git"
            }
          }
        }
      }
    }

    stage('Install Frontend Dependencies') {
      steps {
        dir("${FRONTEND_DIR}") {
          sh 'npm install'
        }
      }
    }

    stage('Build Frontend') {
      steps {
        dir("${FRONTEND_DIR}") {
          sh 'npm run build'
        }
      }
    }

    stage('Set Up Python Backend') {
      steps {
        dir("${BACKEND_DIR}") {
          sh 'python3 -m venv venv'
          sh './venv/bin/pip install -r requirements.txt'
        }
      }
    }

    stage('Start Backend (FastAPI) on Port 8077') {
      steps {
        dir("${BACKEND_DIR}") {
          sh 'pm2 delete backend || true'
          sh 'pm2 start ./venv/bin/uvicorn --name backend -- main:app --host 0.0.0.0 --port 8077'
        }
      }
    }

    stage('Send Team Email Notification') {
      steps {
        script {
          def commitInfo = ""
          def triggeredBy = ""

          dir('AI-Studio-V2') {
            commitInfo = sh(script: 'git log -1 --pretty=format:"%h - %an: %s"', returnStdout: true).trim()
            triggeredBy = sh(script: 'git log -1 --pretty=format:"%an (%ae)"', returnStdout: true).trim()
          }

          def emailSubject = env.BUILD_STATUS == 'SUCCESS' ?
              "✅ Deployment SUCCESS: AI-Studio-V2 [#${env.BUILD_NUMBER}]" :
              "❌ Deployment FAILURE: AI-Studio-V2 [#${env.BUILD_NUMBER}]"

          def emailBody = """
          Job: ${env.JOB_NAME}
          Build: #${env.BUILD_NUMBER}
          Status: ${env.BUILD_STATUS}
          Triggered by: ${triggeredBy}
          Latest Commit: ${commitInfo}
          Frontend: Served via NGINX
          Backend: Running at port 8077
          Build URL: ${env.BUILD_URL}
          """

          mail(
            to: env.TEAM_EMAILS,
            subject: emailSubject,
            body: emailBody
          )
        }
      }
    }
  }

  post {
    failure {
      script {
        env.BUILD_STATUS = 'FAILURE'
        echo "Build failed. Status: ${env.BUILD_STATUS}"
      }
    }

    success {
      echo "Build succeeded. Everything deployed!"
    }

    always {
      echo "GitHub webhook active. Pipeline completed."
    }
  }
}
