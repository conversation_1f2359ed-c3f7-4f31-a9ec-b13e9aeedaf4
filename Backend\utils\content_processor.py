import os
import fitz  # PyMuPDF
import pandas as pd
from PIL import Image
import pytesseract
import openai
import logging
import re
import json
from typing import List, Dict, Tuple, Any, Optional
from io import BytesIO

logger = logging.getLogger(__name__)


class ContentProcessor:
    """Combined utility for image processing, table processing, and content classification"""

    def __init__(self):
        self.openai_model = "gpt-4o"  # Use gpt-4o for vision capabilities

        # Content type detection patterns
        self.TABLE_PATTERNS = [
            r'\|\s*[^|]+\s*\|',  # Markdown table format
            r'^\s*[A-Za-z0-9\s]+\s*\t',  # Tab-separated
            r'^\s*[A-Za-z0-9\s]+\s*,',   # CSV-like
        ]

        self.FIGURE_PATTERNS = [
            r'figure\s*\d+',
            r'image\s*\d+',
            r'chart\s*\d+',
            r'graph\s*\d+',
            r'diagram\s*\d+',
            r'visualization',
        ]

        # Section headers for classification
        self.SECTION_HEADERS = [
            "ABSTRACT", "INTRODUCTION", "METHODS", "METHOD",
            "RESULTS", "DISCUSSION", "CONCLUSION", "REFERENCES",
            "TABLE", "FIGURE", "SUPPLEMENTARY"
        ]

    def process_image_content(self, file_content: bytes, filename: str) -> str:
        """Process image files using OCR or Vision API"""
        try:
            img = Image.open(BytesIO(file_content))

            # Check if it's a scanned document
            if self._is_scanned_image(img):
                return self._extract_ocr_text(img)
            else:
                return self._extract_vision_description(file_content)

        except Exception as e:
            logger.error(f"Image processing failed for {filename}: {e}")
            return f"Image processing failed for {filename}"

    def _is_scanned_image(self, img: Image.Image) -> bool:
        """Detect if image is scanned document"""
        return img.mode == '1' or img.info.get('dpi', (0, 0))[0] > 300

    def _extract_ocr_text(self, img: Image.Image) -> str:
        """Extract text from scanned image using OCR"""
        try:
            ocr_text = pytesseract.image_to_string(img)
            return self._clean_ocr_text(ocr_text)
        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
            return "OCR text extraction failed"

    def _extract_vision_description(self, file_content: bytes) -> str:
        """Extract description from image using GPT-4 Vision"""
        try:
            response = openai.chat.completions.create(
                model=self.openai_model,
                messages=[
                    {"role": "system", "content": "You are an image analyst. Describe only what is visible in the image. Focus on text, labels, data, charts, and structural elements."},
                    {"role": "user", "content": "Describe this image in detail, focusing on any visible data, labels, text, charts, graphs, or structural elements. Include any numerical values, measurements, or quantitative information present."}
                ],
                files=[("image", file_content)],
                max_tokens=500
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Vision API failed: {e}")
            return "Image analysis failed"

    def _clean_ocr_text(self, text: str) -> str:
        """Clean and normalize OCR text"""
        if not text.strip():
            return text

        # Basic cleaning
        cleaned = text.encode('utf-8', errors='ignore').decode('utf-8')
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned)

        # Remove standalone page numbers
        lines = cleaned.split('\n')
        filtered_lines = []
        for line in lines:
            line = line.strip()
            if line and not re.match(r'^\s*\d+\s*$', line):
                filtered_lines.append(line)

        return '\n'.join(filtered_lines).strip()

    def extract_tables_from_pdf(self, pdf_content: bytes) -> List[Dict]:
        """Extract tables from PDF content"""
        tables = []

        try:
            doc = fitz.open(stream=pdf_content, filetype="pdf")

            for page_num, page in enumerate(doc):
                # Find tables using PyMuPDF
                page_tables = page.find_tables(
                    vertical_strategy="lines",
                    horizontal_strategy="lines"
                )

                for table_idx, table in enumerate(page_tables):
                    try:
                        table_data = table.extract()

                        if table_data and len(table_data) > 1:
                            # Convert to markdown format
                            markdown_table = self._convert_table_to_markdown(
                                table_data, page_num + 1, table_idx + 1
                            )

                            if markdown_table:
                                tables.append({
                                    "content": markdown_table,
                                    "page": page_num + 1,
                                    "table_index": table_idx + 1,
                                    "type": "table"
                                })

                    except Exception as e:
                        logger.warning(
                            f"Failed to extract table {table_idx + 1} from page {page_num + 1}: {e}")
                        continue

            doc.close()

        except Exception as e:
            logger.error(f"PDF table extraction failed: {e}")

        return tables

    def _convert_table_to_markdown(self, table_data: List[List[str]], page_num: int, table_num: int) -> str:
        """Convert table data to markdown format"""
        try:
            if not table_data or len(table_data) < 2:
                return ""

            # Clean table data
            cleaned_data = []
            for row in table_data:
                cleaned_row = []
                for cell in row:
                    if cell is None:
                        cleaned_row.append("")
                    else:
                        cleaned_cell = str(cell).strip()
                        cleaned_row.append(cleaned_cell)

                if any(cell.strip() for cell in cleaned_row):
                    cleaned_data.append(cleaned_row)

            if len(cleaned_data) < 2:
                return ""

            # Create markdown table
            markdown_lines = []
            markdown_lines.append(
                f"\n**TABLE {table_num} (Page {page_num})**\n")

            # Header row
            header = cleaned_data[0]
            markdown_lines.append("| " + " | ".join(header) + " |")
            markdown_lines.append(
                "| " + " | ".join(["---"] * len(header)) + " |")

            # Data rows
            for row in cleaned_data[1:]:
                while len(row) < len(header):
                    row.append("")
                markdown_lines.append(
                    "| " + " | ".join(row[:len(header)]) + " |")

            return "\n".join(markdown_lines) + "\n"

        except Exception as e:
            logger.error(f"Table markdown conversion failed: {e}")
            return ""

    def classify_content_type(self, text: str) -> str:
        """Classify content type based on text patterns"""
        text_lower = text.lower()

        # Check for table patterns
        for pattern in self.TABLE_PATTERNS:
            if re.search(pattern, text, re.MULTILINE):
                return "table"

        # Check for figure patterns
        for pattern in self.FIGURE_PATTERNS:
            if re.search(pattern, text_lower):
                return "figure"

        # Check for table-like structure (multiple pipe characters)
        if "|" in text and text.count("|") > 3:
            lines = text.split('\n')
            table_lines = sum(1 for line in lines if line.count("|") > 2)
            if table_lines >= 2:
                return "table"

        return "text"

    def detect_section(self, text: str) -> str:
        """Detect document section based on headers"""
        text_upper = text.upper()

        for header in self.SECTION_HEADERS:
            if header in text_upper:
                return header

        return "OTHER"

    def process_mixed_content(self, text: str) -> List[Dict]:
        """Process text that may contain mixed content types"""
        content_blocks = []

        # Split by potential table boundaries
        lines = text.split('\n')
        current_block = []
        current_type = "text"

        for line in lines:
            line_type = self.classify_content_type(line)

            # If type changes, save current block and start new one
            if line_type != current_type and current_block:
                content_blocks.append({
                    "content": "\n".join(current_block),
                    "type": current_type,
                    "section": self.detect_section("\n".join(current_block))
                })
                current_block = []
                current_type = line_type

            current_block.append(line)

        # Add final block
        if current_block:
            content_blocks.append({
                "content": "\n".join(current_block),
                "type": current_type,
                "section": self.detect_section("\n".join(current_block))
            })

        return content_blocks

    def enhance_text_with_tables(self, text: str, tables: List[Dict]) -> str:
        """Enhance text content with extracted tables"""
        if not tables:
            return text

        enhanced_text = text

        for table in tables:
            # Insert table at appropriate location
            enhanced_text += f"\n\n{table['content']}"

        return enhanced_text

    def create_content_metadata(self, content: str, content_type: str, section: str, page_number: int = None) -> Dict:
        """Create metadata for content chunk"""
        metadata = {
            "content_type": content_type,
            "section": section,
            "token_count": len(content.split()),
            "char_count": len(content),
            "has_tables": "table" in content.lower() or "|" in content,
            "has_figures": any(pattern.replace(r'\d+', '') in content.lower() for pattern in self.FIGURE_PATTERNS)
        }

        # Add page number if provided
        if page_number is not None:
            metadata["page_number"] = page_number

        return metadata

    def extract_text_with_pages(self, file_content: bytes, file_type: str) -> List[Dict]:
        """Extract text from document preserving page information"""
        pages = []

        if file_type.lower() == 'pdf':
            try:
                import PyPDF2
                import io

                pdf_file = io.BytesIO(file_content)
                pdf_reader = PyPDF2.PdfReader(pdf_file)

                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text and page_text.strip():
                            pages.append({
                                "page_number": page_num + 1,
                                "content": page_text.strip(),
                                "type": "text"
                            })
                    except Exception as e:
                        logger.warning(
                            f"Failed to extract text from page {page_num + 1}: {e}")
                        continue

                # Also extract tables with page information
                try:
                    tables = self.extract_tables_from_pdf(file_content)
                    for table in tables:
                        pages.append({
                            "page_number": table.get("page", 1),
                            "content": table["content"],
                            "type": "table",
                            "table_index": table.get("table_index", 1)
                        })
                except Exception as e:
                    logger.warning(f"Table extraction failed: {e}")

            except Exception as e:
                logger.error(f"PDF page extraction failed: {e}")
                # Fallback to single page
                pages.append({
                    "page_number": 1,
                    "content": "Error extracting PDF content",
                    "type": "error"
                })
        else:
            # For non-PDF files, treat as single page
            try:
                if file_type.lower() in ['txt', 'md']:
                    content = file_content.decode('utf-8')
                else:
                    content = str(file_content)

                pages.append({
                    "page_number": 1,
                    "content": content,
                    "type": "text"
                })
            except Exception as e:
                logger.error(f"Text extraction failed for {file_type}: {e}")
                pages.append({
                    "page_number": 1,
                    "content": "Error extracting content",
                    "type": "error"
                })

        return pages
