{"name": "fromtend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "js": "^0.1.0", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "recharts": "^3.0.2", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.10", "three": "^0.178.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^6.3.5"}}