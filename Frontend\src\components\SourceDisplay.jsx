import React, { useState } from 'react';
import { ChevronDown, ChevronRight, FileText, Hash, Zap } from 'lucide-react';

const SourceDisplay = ({ sources, contextUsed, documentMode }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Don't show anything if not in document mode or no sources
  if (!documentMode || !contextUsed || !sources || sources.length === 0) {
    return null;
  }



  const getContentTypeIcon = (contentType) => {
    switch (contentType) {
      case 'table':
        return <Hash className="w-3 h-3" />;
      case 'figure':
        return <Zap className="w-3 h-3" />;
      default:
        return <FileText className="w-3 h-3" />;
    }
  };

  const getContentTypeColor = (contentType) => {
    switch (contentType) {
      case 'table':
        return 'text-green-400';
      case 'figure':
        return 'text-purple-400';
      default:
        return 'text-blue-400';
    }
  };

  const formatSimilarity = (similarity) => {
    if (typeof similarity === 'number') {
      return `${Math.round(similarity * 100)}%`;
    }
    return 'N/A';
  };

  const groupedSources = sources.reduce((acc, source) => {
    const filename = source.filename || 'Unknown Document';
    if (!acc[filename]) {
      acc[filename] = [];
    }
    acc[filename].push(source);
    return acc;
  }, {});

  return (
    <div className="mt-3 border-t border-white/10 pt-3">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 text-xs text-white/60 hover:text-white/80 transition-colors"
      >
        {isExpanded ? (
          <ChevronDown className="w-3 h-3" />
        ) : (
          <ChevronRight className="w-3 h-3" />
        )}
        <span>Sources ({sources.length})</span>
      </button>

      {isExpanded && (
        <div className="mt-2 space-y-2">
          {Object.entries(groupedSources).map(([filename, fileSources]) => (
            <div key={filename} className="bg-white/5 rounded-lg p-3 border border-white/10">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="w-4 h-4 text-blue-400" />
                <span className="text-sm font-medium text-white/90">{filename}</span>
              </div>
              
              <div className="space-y-1">
                {fileSources.map((source, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-2">
                      <div className={`flex items-center gap-1 ${getContentTypeColor(source.content_type)}`}>
                        {getContentTypeIcon(source.content_type)}
                        <span className="capitalize">{source.content_type}</span>
                      </div>
                      
                      {source.page_number && source.page_number !== null && source.page_number !== undefined && (
                        <span className="text-white/60">
                          Page {source.page_number}
                        </span>
                      )}


                      
                      {source.section && source.section !== 'unknown' && (
                        <span className="text-white/50 italic">
                          {source.section}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className="text-white/50">
                        {formatSimilarity(source.similarity)} match
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
          
          <div className="text-xs text-white/40 italic">
            Response generated using information from {Object.keys(groupedSources).length} document(s)
          </div>
        </div>
      )}
    </div>
  );
};

export default SourceDisplay;
