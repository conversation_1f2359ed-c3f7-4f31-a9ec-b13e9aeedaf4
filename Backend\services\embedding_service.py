from langchain_text_splitters import RecursiveCharacterTextSplitter
from sentence_transformers import SentenceTransformer
import uuid
import json
import numpy as np
import openai
import os
from utils.snowflake_setup import get_snowflake_config
import snowflake.connector
from typing import List, Dict, Tuple, Any
from services.reranker import RerankerService
from utils.content_processor import ContentProcessor
from services.session_manager import DocumentSessionManager


class EmbeddingService:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        print("Loading embedding model...")
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        snowflake_config = get_snowflake_config()
        self.snowflake_conn = snowflake.connector.connect(**snowflake_config)

        # Initialize reranker and content processor
        self.reranker = RerankerService()
        self.content_processor = ContentProcessor()
        self.session_manager = DocumentSessionManager()

        # Configuration for enhanced chunking
        self.SECTION_HEADERS = [
            "ABSTRACT", "INTRODUCTION", "METHODS", "METHOD",
            "RESULTS", "DISCUSSION", "CONCLUSION", "REFERENCES",
            "TABLE", "FIGURE", "SUPPLEMENTARY"
        ]

        print("✅ Embedding service initialized")

    def get_snowflake_connection(self):
        """Get a fresh Snowflake connection"""
        snowflake_config = get_snowflake_config()
        return snowflake.connector.connect(**snowflake_config)

    async def process_document(self, doc_id: str, text_content: str):
        print(f"Processing document {doc_id} for chunking and embedding...")

        # Enhanced chunking with content-aware processing
        # Process mixed content first
        content_blocks = self.content_processor.process_mixed_content(
            text_content)

        all_chunks = []
        for block in content_blocks:
            # Use different chunking strategies based on content type
            if block["type"] == "table":
                # Tables get their own chunks (no further splitting)
                all_chunks.append(block["content"])
            elif block["type"] == "figure":
                # Figures get their own chunks
                all_chunks.append(block["content"])
            else:
                # Regular text gets standard chunking
                text_chunks = self.text_splitter.split_text(block["content"])
                all_chunks.extend(text_chunks)

        print(
            f"Created {len(all_chunks)} chunks from {len(content_blocks)} content blocks")

        # Generate embeddings and store
        cursor = self.snowflake_conn.cursor()

        for i, chunk in enumerate(all_chunks):
            chunk_id = str(uuid.uuid4())

            # Generate embedding
            embedding = self.embedding_model.encode(chunk)

            # Store in Snowflake - convert embedding to a format Snowflake can handle
            embedding_list = embedding.tolist()
            # Convert to regular floats to avoid scientific notation and limit precision
            embedding_list = [round(float(x), 6) for x in embedding_list]

            # Store as JSON string in TEXT column
            embedding_json = json.dumps(embedding_list)

            # Create enhanced metadata for the chunk
            content_type = self.content_processor.classify_content_type(chunk)
            section = self.content_processor.detect_section(chunk)
            metadata = self.content_processor.create_content_metadata(
                chunk, content_type, section)
            metadata["chunk_index"] = i
            metadata_json = json.dumps(metadata)

            try:
                cursor.execute("""
                    INSERT INTO document_chunks 
                    (chunk_id, doc_id, chunk_text, chunk_index, embedding_vector, metadata)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (chunk_id, doc_id, chunk, i, embedding_json, metadata_json))
                print(f"Processed chunk {i+1}/{len(all_chunks)}")
            except Exception as e:
                print(f"Error inserting chunk {i}: {e}")
                # Continue with next chunk instead of failing completely
                continue

        self.snowflake_conn.commit()

        # Update document status
        cursor.execute(
            "UPDATE documents SET status = 'processed' WHERE doc_id = %s", (doc_id,))
        self.snowflake_conn.commit()

        print(f"✅ Document {doc_id} processing complete")

        return {
            "doc_id": doc_id,
            "chunks_created": len(all_chunks),
            "status": "processed"
        }

    async def process_document_with_pages(self, doc_id: str, file_content: bytes, file_type: str, filename: str):
        """Process document with page-aware chunking and metadata"""
        print(
            f"Processing document {doc_id} ({filename}) with page information...")

        # Extract text with page information
        pages = self.content_processor.extract_text_with_pages(
            file_content, file_type)
        print(f"Extracted {len(pages)} pages from {filename}")

        all_chunks = []
        cursor = self.snowflake_conn.cursor()

        for page_data in pages:
            page_number = page_data["page_number"]
            page_content = page_data["content"]
            content_type = page_data["type"]

            # Process content based on type
            if content_type == "table":
                # Tables are kept as single chunks
                chunks = [page_content]
            else:
                # Regular text gets chunked
                content_blocks = self.content_processor.process_mixed_content(
                    page_content)
                chunks = []

                for block in content_blocks:
                    if block["type"] == "table":
                        chunks.append(block["content"])
                    elif block["type"] == "figure":
                        chunks.append(block["content"])
                    else:
                        text_chunks = self.text_splitter.split_text(
                            block["content"])
                        chunks.extend(text_chunks)

            # Process each chunk with page information
            for chunk_idx, chunk in enumerate(chunks):
                chunk_id = str(uuid.uuid4())

                # Generate embedding
                embedding = self.embedding_model.encode(chunk)
                embedding_list = embedding.tolist()
                embedding_list = [round(float(x), 6) for x in embedding_list]
                embedding_json = json.dumps(embedding_list)

                # Create enhanced metadata with page information
                detected_content_type = self.content_processor.classify_content_type(
                    chunk)
                section = self.content_processor.detect_section(chunk)
                metadata = self.content_processor.create_content_metadata(
                    chunk, detected_content_type, section, page_number
                )
                metadata["chunk_index"] = len(all_chunks)
                metadata["page_chunk_index"] = chunk_idx
                metadata["filename"] = filename

                # Add table-specific metadata if applicable
                if content_type == "table" and "table_index" in page_data:
                    metadata["table_index"] = page_data["table_index"]

                metadata_json = json.dumps(metadata)

                try:
                    cursor.execute("""
                        INSERT INTO document_chunks
                        (chunk_id, doc_id, chunk_text, chunk_index, embedding_vector, metadata)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (chunk_id, doc_id, chunk, len(all_chunks), embedding_json, metadata_json))

                    all_chunks.append({
                        "chunk_id": chunk_id,
                        "content": chunk,
                        "page_number": page_number,
                        "metadata": metadata
                    })

                    print(
                        f"Processed chunk {len(all_chunks)} (Page {page_number}, Chunk {chunk_idx + 1})")

                except Exception as e:
                    print(
                        f"Error inserting chunk from page {page_number}: {e}")
                    continue

        self.snowflake_conn.commit()

        # Update document status
        cursor.execute(
            "UPDATE documents SET status = 'processed' WHERE doc_id = %s", (doc_id,))
        self.snowflake_conn.commit()

        print(
            f"✅ Successfully processed document {doc_id} with {len(all_chunks)} chunks across {len(pages)} pages")

        return {
            "doc_id": doc_id,
            "chunks_created": len(all_chunks),
            "pages_processed": len(pages),
            "status": "processed"
        }

    async def find_relevant_chunks(self, question: str, top_k: int = 5, session_id: str = None):
        """Enhanced retrieval with reranking and content-type awareness - SESSION SCOPED"""
        print(f"Searching for relevant chunks for: {question}")

        # Get current session if none provided
        if session_id is None:
            session_id = self.session_manager.get_current_session_id()

        if not session_id:
            print("❌ No active session found")
            return None

        # Get documents for this session
        session_doc_ids = self.session_manager.get_documents_for_session(
            session_id)
        if not session_doc_ids:
            print(f"❌ No documents found in session {session_id}")
            return None

        print(
            f"🔍 Searching in session {session_id} with {len(session_doc_ids)} documents")

        # Generate question embedding
        question_embedding = self.embedding_model.encode(question)
        question_embedding_list = question_embedding.tolist()
        question_embedding_list = [round(float(x), 6)
                                   for x in question_embedding_list]

        cursor = self.snowflake_conn.cursor()

        try:
            # Get chunks only from the current session's documents
            placeholders = ','.join(['%s'] * len(session_doc_ids))
            cursor.execute(f"""
                SELECT 
                    dc.chunk_text,
                    d.filename,
                    dc.embedding_vector,
                    dc.metadata
                FROM document_chunks dc
                JOIN documents d ON dc.doc_id = d.doc_id
                WHERE d.doc_id IN ({placeholders})
                AND d.status = 'processed'
            """, session_doc_ids)

            chunks_with_similarity = []
            for chunk_text, filename, embedding_json, metadata_json in cursor.fetchall():
                try:
                    # Parse the stored embedding and metadata
                    stored_embedding = json.loads(embedding_json)
                    metadata = json.loads(
                        metadata_json) if metadata_json else {}

                    # Compute cosine similarity
                    similarity = self._cosine_similarity(
                        question_embedding_list, stored_embedding)

                    # Create chunk object with metadata
                    chunk_obj = {
                        "text": chunk_text,
                        "metadata": metadata
                    }

                    chunks_with_similarity.append(
                        (chunk_obj, filename, similarity))
                except Exception as e:
                    print(f"Error processing chunk similarity: {e}")
                    continue

            # Apply reranking
            reranked_chunks = await self._rerank_chunks(question, chunks_with_similarity, top_k)

        except Exception as e:
            print(f"Error in similarity search: {e}")
            # Fallback to simple text search within session
            placeholders = ','.join(['%s'] * len(session_doc_ids))
            cursor.execute(f"""
                SELECT 
                    dc.chunk_text,
                    d.filename
                FROM document_chunks dc
                JOIN documents d ON dc.doc_id = d.doc_id
                WHERE d.doc_id IN ({placeholders})
                AND d.status = 'processed'
                LIMIT %s
            """, session_doc_ids + [top_k])

            reranked_chunks = [(chunk[0], chunk[1], 0.5)
                               for chunk in cursor.fetchall()]

        if not reranked_chunks:
            print(f"❌ No relevant documents found in session {session_id}")
            return None

        print(
            f"✅ Found {len(reranked_chunks)} relevant chunks in session {session_id}")

        # Build context and sources with enhanced metadata
        context_parts = []
        sources = []

        for chunk_data, filename, similarity in reranked_chunks:
            if isinstance(chunk_data, dict):
                # New format with metadata
                chunk_text = chunk_data["text"]
                metadata = chunk_data.get("metadata", {})
                page_number = metadata.get("page_number")

                # Build context with page information
                if page_number:
                    context_parts.append(
                        f"[From {filename}, Page {page_number}]: {chunk_text}")
                    sources.append({
                        "filename": filename,
                        "page_number": page_number,
                        "similarity": round(similarity, 2),
                        "content_type": metadata.get("content_type", "text"),
                        "section": metadata.get("section", "unknown")
                    })
                else:
                    context_parts.append(f"[From {filename}]: {chunk_text}")
                    sources.append({
                        "filename": filename,
                        "page_number": None,
                        "similarity": round(similarity, 2),
                        "content_type": metadata.get("content_type", "text"),
                        "section": metadata.get("section", "unknown")
                    })
            else:
                # Legacy format (plain text)
                context_parts.append(f"[From {filename}]: {chunk_data}")
                sources.append({
                    "filename": filename,
                    "page_number": None,
                    "similarity": round(similarity, 2),
                    "content_type": "text",
                    "section": "unknown"
                })

        context = "\n\n".join(context_parts)

        return {
            "context": context,
            "sources": sources
        }

    async def _rerank_chunks(self, query: str, chunks_with_similarity: List[Tuple], top_k: int) -> List[Tuple]:
        """Apply advanced reranking using the dedicated reranker service"""
        if not chunks_with_similarity:
            return []

        if len(chunks_with_similarity) <= top_k:
            return chunks_with_similarity

        # Convert to format expected by reranker service
        chunks_for_reranking = []
        for chunk_obj, filename, base_score in chunks_with_similarity:
            chunks_for_reranking.append({
                "text": chunk_obj["text"],
                "metadata": chunk_obj["metadata"],
                "filename": filename,
                "base_score": base_score
            })

        # Apply reranking using the dedicated reranker service
        try:
            reranked_results = await self.reranker.batch_rerank(query, chunks_for_reranking, top_k)

            # Convert back to original format
            reranked_chunks = []
            for result in reranked_results:
                passage = result["passage"]
                score = result["score"]
                reranked_chunks.append(
                    (passage["text"], passage["filename"], score))

            print(
                f"✅ Reranking completed successfully with {len(reranked_chunks)} chunks")
            return reranked_chunks

        except Exception as e:
            print(f"⚠️ Reranking failed, falling back to base similarity: {e}")
            # Fallback: sort by base similarity and return top_k
            chunks_with_similarity.sort(key=lambda x: x[2], reverse=True)
            return chunks_with_similarity[:top_k]

    def _cosine_similarity(self, vec1, vec2):
        """Compute cosine similarity between two vectors"""
        try:
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)

            # Normalize vectors
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            # Compute cosine similarity
            similarity = np.dot(vec1, vec2) / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            print(f"Error computing similarity: {e}")
            return 0.0

    async def check_query_relevance(self, query: str, session_id: str = None) -> Dict:
        """Check if query is relevant to uploaded documents in current session"""
        try:
            # Get current session if none provided
            if session_id is None:
                session_id = self.session_manager.get_current_session_id()

            if not session_id:
                return {
                    "is_relevant": False,
                    "relevance_score": 0.0,
                    "matched_documents": [],
                    "scope_message": "No active session",
                    "reason": "no_session"
                }

            # Check if there are active documents in this session
            session_docs = self.session_manager.get_documents_for_session(
                session_id)
            if not session_docs:
                return {
                    "is_relevant": False,
                    "relevance_score": 0.0,
                    "matched_documents": [],
                    "scope_message": f"No documents in session {session_id}",
                    "reason": "no_documents_in_session"
                }

            print(
                f"🔍 Checking relevance for session {session_id} with {len(session_docs)} documents")

            # Generate query embedding
            query_embedding = self.embedding_model.encode(query)
            query_embedding_list = [round(float(x), 6)
                                    for x in query_embedding.tolist()]

            cursor = self.snowflake_conn.cursor()

            # Get chunks from this session's documents only
            placeholders = ','.join(['%s'] * len(session_docs))
            cursor.execute(f"""
                SELECT 
                    dc.chunk_text,
                    d.filename,
                    dc.embedding_vector
                FROM document_chunks dc
                JOIN documents d ON dc.doc_id = d.doc_id
                WHERE d.doc_id IN ({placeholders})
                AND d.status = 'processed'
            """, session_docs)

            chunks = cursor.fetchall()

            if not chunks:
                return {
                    "is_relevant": False,
                    "relevance_score": 0.0,
                    "matched_documents": [],
                    "scope_message": f"No processed chunks in session {session_id}",
                    "reason": "no_processed_chunks"
                }

            # Calculate relevance scores
            relevance_scores = []
            for chunk_text, filename, embedding_json in chunks:
                try:
                    stored_embedding = json.loads(embedding_json)
                    similarity = self._cosine_similarity(
                        query_embedding_list, stored_embedding)
                    relevance_scores.append({
                        "filename": filename,
                        "similarity": similarity,
                        "chunk_preview": chunk_text[:100] + "..." if len(chunk_text) > 100 else chunk_text
                    })
                except Exception as e:
                    print(f"Error processing chunk relevance: {e}")
                    continue

            if not relevance_scores:
                return {
                    "is_relevant": False,
                    "relevance_score": 0.0,
                    "matched_documents": [],
                    "scope_message": f"Error processing chunks in session {session_id}",
                    "reason": "processing_error"
                }

            # Sort by relevance and get top matches
            relevance_scores.sort(key=lambda x: x["similarity"], reverse=True)
            top_matches = relevance_scores[:3]
            avg_relevance = sum(r["similarity"]
                                for r in top_matches) / len(top_matches)

            # Determine if query is relevant
            is_relevant = avg_relevance > 0.3  # Threshold for relevance

            return {
                "is_relevant": is_relevant,
                "relevance_score": avg_relevance,
                "matched_documents": top_matches,
                "scope_message": f"Query relevance: {avg_relevance:.2f} in session {session_id}",
                "reason": "relevance_calculated",
                "session_id": session_id,
                "total_documents": len(session_docs)
            }

        except Exception as e:
            print(f"❌ Error checking query relevance: {e}")
            return {
                "is_relevant": False,
                "relevance_score": 0.0,
                "matched_documents": [],
                "scope_message": f"Error: {str(e)}",
                "reason": "exception"
            }
